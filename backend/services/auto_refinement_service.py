"""
Auto-refinement service that uses vector database to answer clarifying questions automatically.
Uses Gemini models via OpenAI-compatible endpoint.
"""

import logging
from typing import List, Dict, Any, Optional
from vector_db.pinecone_client import PineconeVectorDB
from prompt_refinement.analyzer import PromptA<PERSON>yzer
from prompt_refinement.config import RefinementConfig
import openai
import os

logger = logging.getLogger(__name__)


class AutoRefinementService:
    """Service for automatic prompt refinement using vector database and Gemini models."""
    
    def __init__(self):
        """Initialize the auto-refinement service."""
        self.analyzer = PromptAnalyzer()
        self.config = RefinementConfig()

        # Initialize client to use the local proxy endpoint
        api_key = os.getenv("OPENAI_API_KEY", "dummy_key")  # Use dummy key for local proxy
        api_url = os.getenv("BACKEND_URL", "http://localhost:8001")  # Use configurable backend URL
        self.openai_client = openai.OpenAI(api_key=api_key, base_url=api_url)

        # Initialize vector DB (but don't fail if it's not available)
        self.vector_db_available = True
        try:
            self.vector_db = PineconeVectorDB()
            logger.info("Vector database initialized successfully")
        except Exception as e:
            logger.warning(f"Vector database not available: {e}")
            self.vector_db_available = False
            self.vector_db = None

    def search_knowledge_base(self, query: str, top_k: int = 5, user_id: str = "demo_user", project_id: str = "demo_project") -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant information using new API structure."""
        if not self.vector_db_available or not self.vector_db:
            logger.warning("Vector database not available for search")
            return []

        try:
            # Use the new query_by_project method for searching
            return self.vector_db.query_by_project(
                user_id=user_id,
                project_id=project_id,
                query_text=query,
                top_k=top_k
            )
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    def generate_answer_from_context(self, question: str, context_results: List[Dict[str, Any]]) -> str:
        """Generate an answer to a question using context from vector database."""
        try:
            # Use a lower threshold for relevance to get more context
            relevant_results = [r for r in context_results if r.get('score', 0) > 0.6]
            high_relevance_results = [r for r in context_results if r.get('score', 0) > 0.75]

            # Prefer high relevance but fall back to relevant results
            results_to_use = high_relevance_results if high_relevance_results else relevant_results

            if not results_to_use:
                logger.info(f"No relevant context found for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            # Prepare context from search results with better formatting
            context_texts = []
            for i, result in enumerate(results_to_use[:7]):  # Use up to 7 results for more context
                context_texts.append(f"Context {i+1} (Relevance: {result['score']:.3f}):\n{result['text']}")

            context_text = "\n\n".join(context_texts)

            system_prompt = """You are an expert assistant helping with browser automation testing. You answer questions based ONLY on the provided context from a knowledge base.

CRITICAL ANTI-HALLUCINATION RULES:
1. ONLY use information explicitly stated in the provided context - DO NOT invent or assume any details
2. If the context contains relevant information, provide a specific, actionable answer with exact elements and steps
3. If the context doesn't contain enough information, respond with: "INSUFFICIENT_CONTEXT: Please provide more specific details about [specific missing information]"
4. NEVER make up element names, IDs, classes, URLs, or any technical details not in the context
5. NEVER assume the existence of buttons, forms, or elements not explicitly mentioned in the context
6. If asked about verification steps, only mention verification methods explicitly described in the context

RESPONSE FORMAT REQUIREMENTS:
- Be specific and structured in your answers
- Include exact element identifiers when available in context
- Provide step-by-step instructions when the context contains procedural information
- Focus on actionable details for browser automation (specific elements, steps, verification)
- Synthesize information from multiple context pieces when relevant, but only if they are explicitly provided"""

            user_message = f"""Question: {question}

Available Context:
{context_text}

Based ONLY on the above context, provide a specific, actionable answer for browser automation. Structure your response as follows:

If sufficient context is available:
- Provide exact element identifiers (IDs, classes, text content) from the context
- List specific step-by-step actions to perform
- Include precise verification methods mentioned in the context
- Reference specific pages/URLs if mentioned in the context

If context is insufficient:
Respond with: "INSUFFICIENT_CONTEXT: Please provide more specific details about [what is missing]"

Remember: DO NOT invent any element names, selectors, URLs, or verification methods not explicitly mentioned in the provided context."""

            response = self.openai_client.chat.completions.create(
                model=os.getenv("AUTO_REFINEMENT_MODEL", "planner"),  # Use proxy model name
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.1
            )

            # Handle potential null response from Gemini API
            response_content = response.choices[0].message.content
            if response_content is None:
                logger.warning(f"Received null response from Gemini API for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            answer = response_content.strip()

            # Check if the model indicated insufficient information or provided generic responses
            insufficient_indicators = [
                "don't have enough", "insufficient", "not enough information",
                "cannot determine", "unclear from the context", "not specified",
                "insufficient_context:", "please provide more specific details",
                "not mentioned in the context", "context does not contain"
            ]

            generic_responses = [
                "click the button", "fill the form", "navigate to the page",
                "check the result", "verify success", "complete the task"
            ]

            # Check for insufficient context indicators
            has_insufficient_context = any(indicator in answer.lower() for indicator in insufficient_indicators)
            # Check for generic responses that might indicate hallucination
            has_generic_response = any(generic.lower() in answer.lower() for generic in generic_responses)

            if has_insufficient_context or (has_generic_response and not any(result['score'] > 0.8 for result in results_to_use)):
                logger.info(f"Model indicated insufficient context or provided generic response for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            logger.info(f"Generated answer from context: {answer[:50]}...")
            return answer

        except Exception as e:
            logger.error(f"Error generating answer from context: {e}")
            return self._get_conservative_fallback_answer(question)

    def _get_conservative_fallback_answer(self, question: str) -> str:
        """Get a conservative fallback answer when context is insufficient."""
        question_lower = question.lower()
        
        if "page" in question_lower or "navigate" in question_lower:
            return "Navigate to the specified URL and verify the page loads successfully"
        elif "button" in question_lower or "click" in question_lower:
            return "Look for the primary action button on the page and click it to activate the feature"
        elif "field" in question_lower or "input" in question_lower or "form" in question_lower:
            return "Find the main input fields or form on the page and interact with them using appropriate test data"
        elif "verify" in question_lower or "success" in question_lower or "validation" in question_lower:
            return "Check for visual feedback, page changes, or successful completion indicators after performing the action"
        elif "select" in question_lower or "dropdown" in question_lower or "choose" in question_lower:
            return "Locate dropdown menus or selection options and choose appropriate test values"
        elif "data" in question_lower or "information" in question_lower or "details" in question_lower:
            return "Use standard test data appropriate for the type of inputs (e.g., valid email formats, typical user information)"
        elif "test" in question_lower or "feature" in question_lower or "functionality" in question_lower:
            return "Interact with the main elements of the feature and verify it responds appropriately to user actions"
        else:
            return "Explore and interact with the main interactive elements on the page to test the functionality"

    def auto_refine_prompt(self, original_prompt: str) -> Dict[str, Any]:
        """
        Automatically refine a prompt using vector database for context.
        Single iteration to prevent over-refinement and hallucination.

        Args:
            original_prompt: The original user prompt

        Returns:
            Dictionary with refinement results and iteration logs
        """
        logger.info(f"Starting auto-refinement for prompt: {original_prompt[:100]}...")

        # Initialize tracking - single iteration only
        current_prompt = original_prompt
        iteration_logs = []
        max_iterations = 1  # Fixed at 1 to prevent over-refinement

        try:
            # Analyze the prompt
            analysis = self.analyzer.analyze_prompt(original_prompt)

            # Log the force refinement status
            if analysis.get('force_refinement_enabled', False):
                logger.info("Force auto refinement is ENABLED - will refine regardless of thresholds")
            else:
                logger.info("Force auto refinement is DISABLED - using original threshold logic")

            # Check if refinement is needed
            if not analysis['needs_refinement']:
                logger.info("Prompt doesn't need refinement")
                return {
                    "success": True,
                    "original_prompt": original_prompt,
                    "final_prompt": original_prompt,
                    "iterations_completed": 0,
                    "max_iterations": max_iterations,
                    "final_specificity_score": analysis['specificity_score'],
                    "final_semantic_score": analysis['semantic_score'],
                    "thresholds_met": True,
                    "iteration_logs": [],
                    "final_analysis": analysis
                }

            # Single iteration refinement
            iteration = 1
            logger.info(f"Starting iteration {iteration}")

            # Generate clarifying questions
            questions = self.analyzer.generate_questions(current_prompt, analysis['search_results'])
            logger.info(f"Generated {len(questions)} questions")

            # Auto-answer questions using vector database
            answers = []
            question_contexts = []

            for question in questions:
                # Search knowledge base for relevant context with more chunks
                # Also search using the original prompt for additional context
                question_results = self.search_knowledge_base(question, top_k=8)
                prompt_results = self.search_knowledge_base(original_prompt, top_k=5)

                # Combine and deduplicate results
                combined_results = question_results + prompt_results
                seen_ids = set()
                unique_results = []
                for result in combined_results:
                    if result['id'] not in seen_ids:
                        unique_results.append(result)
                        seen_ids.add(result['id'])

                # Sort by relevance score
                unique_results.sort(key=lambda x: x.get('score', 0), reverse=True)

                question_contexts.append({
                    "question": question,
                    "search_results": unique_results[:10]  # Top 10 unique results
                })

                # Generate answer from context
                answer = self.generate_answer_from_context(question, unique_results[:10])
                answers.append(answer)

                logger.info(f"Q: {question[:50]}... A: {answer[:50]}...")

            # Refine the prompt with the generated answers
            refined_prompt = self.analyzer.refine_prompt(current_prompt, questions, answers)

            # Re-analyze the refined prompt
            refined_analysis = self.analyzer.analyze_prompt(refined_prompt)

            # Check if thresholds are met
            thresholds_met = (
                refined_analysis['specificity_score'] >= self.config.SPECIFICITY_THRESHOLD and
                refined_analysis['semantic_score'] >= self.config.SEMANTIC_SCORE_THRESHOLD
            )

            # Log this iteration
            iteration_log = {
                "iteration": iteration,
                "questions": questions,
                "answers": answers,
                "specificity_score": refined_analysis['specificity_score'],
                "semantic_score": refined_analysis['semantic_score'],
                "thresholds_met": thresholds_met,
                "refined_prompt": refined_prompt
            }
            iteration_logs.append(iteration_log)

            logger.info(f"Iteration {iteration} completed - Specificity: {refined_analysis['specificity_score']:.3f}, Semantic: {refined_analysis['semantic_score']:.3f}")

            # Update current prompt
            current_prompt = refined_prompt

            return {
                "success": True,
                "original_prompt": original_prompt,
                "final_prompt": current_prompt,
                "iterations_completed": iteration,
                "max_iterations": max_iterations,
                "final_specificity_score": refined_analysis['specificity_score'],
                "final_semantic_score": refined_analysis['semantic_score'],
                "thresholds_met": thresholds_met,
                "iteration_logs": iteration_logs,
                "final_analysis": refined_analysis
            }

        except Exception as e:
            logger.error(f"Auto-refinement failed: {e}")
            return {
                "success": False,
                "original_prompt": original_prompt,
                "final_prompt": original_prompt,
                "iterations_completed": 0,
                "max_iterations": max_iterations,
                "final_specificity_score": 0.3,
                "final_semantic_score": 0.3,
                "thresholds_met": False,
                "iteration_logs": iteration_logs,
                "error": str(e)
            }
