"""
Vector Database Service for managing Pinecone database operations
"""

import logging
import uuid
from typing import List, Dict, Any, Optional
from fastapi import UploadFile
import aiofiles
import tempfile
import os
from datetime import datetime
from vector_db.pinecone_client import PineconeVectorDB

# Try to import PDF processing
try:
    import PyPDF2
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

# Try to import DOCX processing
try:
    from docx import Document as DocxDocument
    DOCX_SUPPORT = True
except ImportError:
    DOCX_SUPPORT = False

logger = logging.getLogger(__name__)


class VectorDBService:
    """Service for managing vector database operations."""
    
    def __init__(self):
        """Initialize the vector database service."""
        try:
            self.vector_db = PineconeVectorDB()
            self.vector_db.create_index_if_not_exists()
            logger.info("Vector database service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize vector database service: {e}")
            raise
    
    async def add_document(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Add a single document to the vector database."""
        try:
            if metadata is None:
                metadata = {}
            
            documents = [{"text": text, "metadata": metadata}]
            self.vector_db.upsert_documents(documents)
            
            document_id = self.vector_db.generate_id(text)
            
            return {
                "success": True,
                "document_id": document_id,
                "message": "Document added successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to add document: {e}")
            raise
    
    async def add_documents_batch(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Add multiple documents to the vector database."""
        try:
            self.vector_db.upsert_documents(documents)
            
            return {
                "success": True,
                "documents_added": len(documents),
                "message": f"Successfully added {len(documents)} documents"
            }
            
        except Exception as e:
            logger.error(f"Failed to add documents batch: {e}")
            raise
    
    async def search(self, query: str, top_k: int = 5, filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar documents in the vector database (legacy method using new implementation)."""
        try:
            # Use new implementation with default user/project values for backward compatibility
            results = await self.query_project_documents(
                query=query,
                user_id="demo_user",
                project_id="demo_project",
                top_k=top_k
            )

            # Convert new format to legacy format
            legacy_results = []
            for result in results:
                legacy_results.append({
                    "id": result.get("file_id", "unknown"),
                    "text": result["text"],
                    "score": result["score"],
                    "metadata": result.get("metadata", {})
                })

            return legacy_results

        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    async def delete_document(self, document_id: str) -> Dict[str, Any]:
        """Delete a document from the vector database."""
        try:
            self.vector_db.delete_document(document_id)
            
            return {
                "success": True,
                "message": f"Document {document_id} deleted successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to delete document: {e}")
            raise
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            stats = self.vector_db.get_stats()
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            raise
    
    async def clear_database(self) -> Dict[str, Any]:
        """Clear all documents from the vector database."""
        try:
            self.vector_db.clear_all()
            
            return {
                "success": True,
                "message": "Database cleared successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to clear database: {e}")
            raise
    
    async def upload_and_process_file(self, file: UploadFile, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Upload and process a document file."""
        try:
            if metadata is None:
                metadata = {}
            
            # Add file metadata
            metadata.update({
                "filename": file.filename,
                "content_type": file.content_type,
                "file_size": file.size if hasattr(file, 'size') else None
            })
            
            # Read file content
            content = await file.read()
            
            # Process based on file type
            if file.content_type == "text/plain" or file.filename.endswith('.txt'):
                text_content = content.decode('utf-8')
                chunks = self._chunk_text(text_content)
            elif file.filename.endswith('.md'):
                text_content = content.decode('utf-8')
                chunks = self._chunk_text(text_content)
            else:
                # For other file types, treat as plain text for now
                try:
                    text_content = content.decode('utf-8')
                    chunks = self._chunk_text(text_content)
                except UnicodeDecodeError:
                    raise ValueError(f"Unsupported file type: {file.content_type}")
            
            # Create documents for each chunk
            documents = []
            for i, chunk in enumerate(chunks):
                chunk_metadata = metadata.copy()
                chunk_metadata.update({
                    "chunk_index": i,
                    "total_chunks": len(chunks)
                })
                documents.append({
                    "text": chunk,
                    "metadata": chunk_metadata
                })
            
            # Add documents to vector database
            self.vector_db.upsert_documents(documents)
            
            return {
                "success": True,
                "chunks_added": len(documents),
                "message": f"File processed and {len(documents)} chunks added"
            }
            
        except Exception as e:
            logger.error(f"Failed to upload and process file: {e}")
            raise
    
    def _chunk_text(self, text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """Split text into chunks for processing."""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # If this isn't the last chunk, try to break at a sentence or word boundary
            if end < len(text):
                # Look for sentence boundary
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start + chunk_size // 2:
                    end = sentence_end + 1
                else:
                    # Look for word boundary
                    word_end = text.rfind(' ', start, end)
                    if word_end > start + chunk_size // 2:
                        end = word_end
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - chunk_overlap
            if start >= len(text):
                break
        
        return chunks

    def _extract_text_from_file(self, file_content: bytes, filename: str) -> str:
        """Extract text from different file types."""
        file_ext = os.path.splitext(filename)[1].lower()

        if file_ext in ['.txt', '.md']:
            return file_content.decode('utf-8')

        elif file_ext == '.docx':
            if not DOCX_SUPPORT:
                raise ValueError("DOCX support not available. Please install python-docx.")

            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                temp_file.write(file_content)
                temp_file.flush()

                try:
                    doc = DocxDocument(temp_file.name)
                    text = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
                    return text
                finally:
                    try:
                        os.unlink(temp_file.name)
                    except OSError:
                        pass

        elif file_ext == '.pdf':
            if not PDF_SUPPORT:
                raise ValueError("PDF support not available. Please install PyPDF2.")

            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(file_content)
                temp_file.flush()

                try:
                    with open(temp_file.name, 'rb') as pdf_file:
                        pdf_reader = PyPDF2.PdfReader(pdf_file)
                        text = ""
                        for page in pdf_reader.pages:
                            text += page.extract_text() + "\n"
                        return text
                finally:
                    try:
                        os.unlink(temp_file.name)
                    except OSError:
                        pass

        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

    def _generate_file_url(self, user_id: str, project_id: str, filename: str) -> str:
        """Generate a simulated file URL for storage reference."""
        # In a real implementation, this would be an actual file storage URL
        return f"https://storage.drcode.com/{user_id}/{project_id}/{filename}"

    async def upload_file_with_context(self, file: UploadFile, user_id: str = "demo_user",
                                     project_id: str = "demo_project") -> Dict[str, Any]:
        """
        Upload and process a file with user/project context using the new schema.

        Args:
            file: The uploaded file
            user_id: User identifier (defaults to demo value)
            project_id: Project identifier (defaults to demo value)

        Returns:
            Dictionary with upload results
        """
        try:
            if not file.filename:
                raise ValueError("No filename provided")

            # Generate unique file ID
            file_id = str(uuid.uuid4())

            # Read file content
            content = await file.read()

            # Extract text from file
            text_content = self._extract_text_from_file(content, file.filename)

            if not text_content.strip():
                raise ValueError("File appears to be empty or contains no extractable text")

            # Generate file URL (simulated)
            file_url = self._generate_file_url(user_id, project_id, file.filename)

            # Chunk the text
            chunks = self._chunk_text(text_content)

            # Index each chunk
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                chunk_id = self.vector_db.index_file_chunk(
                    user_id=user_id,
                    project_id=project_id,
                    file_id=file_id,
                    filename=file.filename,
                    file_url=file_url,
                    text_chunk=chunk,
                    chunk_index=i
                )
                chunk_ids.append(chunk_id)

            return {
                "success": True,
                "file_id": file_id,
                "filename": file.filename,
                "file_url": file_url,
                "chunks_added": len(chunks),
                "chunk_ids": chunk_ids,
                "user_id": user_id,
                "project_id": project_id,
                "message": f"File processed and {len(chunks)} chunks indexed successfully"
            }

        except Exception as e:
            logger.error(f"Failed to upload file with context: {e}")
            raise

    async def query_project_documents(self, query: str, user_id: str = "demo_user",
                                    project_id: str = "demo_project", top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Query documents within a specific project.

        Args:
            query: Search query text
            user_id: User identifier (defaults to demo value)
            project_id: Project identifier (defaults to demo value)
            top_k: Number of results to return

        Returns:
            List of matching document chunks
        """
        try:
            results = self.vector_db.query_by_project(
                user_id=user_id,
                project_id=project_id,
                query_text=query,
                top_k=top_k
            )

            return results

        except Exception as e:
            logger.error(f"Failed to query project documents: {e}")
            raise

    async def query_file_documents(self, query: str, file_id: str, user_id: str = "demo_user",
                                 project_id: str = "demo_project", top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Query documents within a specific file.

        Args:
            query: Search query text
            file_id: File identifier to search within
            user_id: User identifier (defaults to demo value)
            project_id: Project identifier (defaults to demo value)
            top_k: Number of results to return

        Returns:
            List of matching document chunks from the specific file
        """
        try:
            results = self.vector_db.query_by_file(
                user_id=user_id,
                project_id=project_id,
                file_id=file_id,
                query_text=query,
                top_k=top_k
            )

            return results

        except Exception as e:
            logger.error(f"Failed to query file documents: {e}")
            raise

    async def list_files_by_project(self, user_id: str = "demo_user", project_id: str = "demo_project") -> List[Dict[str, Any]]:
        """
        List all files in a project.

        Args:
            user_id: User identifier (defaults to demo value)
            project_id: Project identifier (defaults to demo value)

        Returns:
            List of files with metadata
        """
        try:
            files = self.vector_db.list_files_by_project(
                user_id=user_id,
                project_id=project_id
            )

            return files

        except Exception as e:
            logger.error(f"Failed to list files for project: {e}")
            raise

    async def delete_file_by_id(self, file_id: str, user_id: str = "demo_user", project_id: str = "demo_project") -> Dict[str, Any]:
        """
        Delete a file and all its chunks.

        Args:
            file_id: File identifier to delete
            user_id: User identifier (defaults to demo value)
            project_id: Project identifier (defaults to demo value)

        Returns:
            Dictionary with deletion results
        """
        try:
            success = self.vector_db.delete_file_by_id(
                user_id=user_id,
                project_id=project_id,
                file_id=file_id
            )

            if success:
                return {
                    "success": True,
                    "message": f"File {file_id} deleted successfully",
                    "file_id": file_id
                }
            else:
                return {
                    "success": False,
                    "message": f"File {file_id} not found or could not be deleted",
                    "file_id": file_id
                }

        except Exception as e:
            logger.error(f"Failed to delete file: {e}")
            raise

    async def get_file_metadata(self, file_id: str, user_id: str = "demo_user", project_id: str = "demo_project") -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific file.

        Args:
            file_id: File identifier
            user_id: User identifier (defaults to demo value)
            project_id: Project identifier (defaults to demo value)

        Returns:
            File metadata if found, None otherwise
        """
        try:
            metadata = self.vector_db.get_file_metadata(
                user_id=user_id,
                project_id=project_id,
                file_id=file_id
            )

            return metadata

        except Exception as e:
            logger.error(f"Failed to get file metadata: {e}")
            raise
