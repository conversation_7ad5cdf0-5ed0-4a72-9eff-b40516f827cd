"""
Prompt Analysis and Refinement System

This module analyzes user prompts against the vector database to determine
if they need clarification and generates appropriate questions.
Uses Gemini models via OpenAI-compatible endpoint.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from vector_db.pinecone_client import PineconeVectorDB
from prompt_refinement.config import RefinementConfig

logger = logging.getLogger(__name__)


class PromptAnalyzer:
    """Analyzes prompts and determines if they need refinement using Gemini models."""
    
    def __init__(self):
        """Initialize the prompt analyzer."""
        self.vector_db = PineconeVectorDB()
        self.vector_db.create_index_if_not_exists()
        
        # Initialize client to use the local proxy endpoint
        api_key = os.getenv("OPENAI_API_KEY", "dummy_key")  # Use dummy key for local proxy
        api_url = "http://localhost:8001"  # Use local proxy instead of direct Gemini API
        self.openai_client = openai.OpenAI(
            api_key=api_key,
            base_url=api_url
        )
        
        # Load configuration
        self.config = RefinementConfig()
        self.min_similarity_threshold = self.config.MIN_SIMILARITY_THRESHOLD
        self.vague_prompt_indicators = self.config.VAGUE_INDICATORS
        
    def analyze_prompt(self, user_prompt: str, user_id: str = "demo_user", project_id: str = "demo_project") -> Dict[str, Any]:
        """
        Analyze a user prompt to determine if it needs refinement.

        Args:
            user_prompt: The user's input prompt
            user_id: User identifier for namespace isolation
            project_id: Project identifier for context filtering

        Returns:
            Dictionary containing analysis results
        """
        try:
            # Search vector database for relevant context using new API structure
            search_results = self.vector_db.query_by_project(
                user_id=user_id,
                project_id=project_id,
                query_text=user_prompt,
                top_k=self.config.VECTOR_SEARCH_TOP_K
            )
            
            # Analyze prompt specificity
            specificity_score = self._calculate_specificity_score(user_prompt)

            # Calculate semantic score
            semantic_score = self._calculate_semantic_score(user_prompt, search_results)

            # Check if prompt has sufficient context
            has_sufficient_context = self._has_sufficient_context(user_prompt, search_results)
            
            # Determine if refinement is needed
            # If FORCE_AUTO_REFINEMENT is enabled, always refine regardless of thresholds
            if self.config.FORCE_AUTO_REFINEMENT:
                needs_refinement = True
                logger.info("Force auto refinement enabled - bypassing threshold checks")
            else:
                needs_refinement = (
                    specificity_score < self.config.MIN_SPECIFICITY_THRESHOLD or
                    not has_sufficient_context or
                    len(user_prompt.split()) < self.config.MIN_PROMPT_LENGTH
                )
            
            return {
                'needs_refinement': needs_refinement,
                'force_refinement_enabled': self.config.FORCE_AUTO_REFINEMENT,
                'specificity_score': specificity_score,
                'semantic_score': semantic_score,
                'has_sufficient_context': has_sufficient_context,
                'search_results': search_results,
                'word_count': len(user_prompt.split()),
                'analysis_details': {
                    'vague_indicators_found': self._find_vague_indicators(user_prompt),
                    'specific_elements_mentioned': self._find_specific_elements(user_prompt),
                    'action_clarity': self._assess_action_clarity(user_prompt)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing prompt: {e}")
            # Return safe defaults
            return {
                'needs_refinement': True,
                'force_refinement_enabled': self.config.FORCE_AUTO_REFINEMENT,
                'specificity_score': 0.3,
                'semantic_score': 0.3,
                'has_sufficient_context': False,
                'search_results': [],
                'word_count': len(user_prompt.split()),
                'analysis_details': {
                    'error': str(e)
                }
            }
    
    def _calculate_specificity_score(self, prompt: str) -> float:
        """Calculate how specific the prompt is."""
        try:
            words = prompt.lower().split()
            word_count = len(words)

            # Base score from word count (more generous)
            length_score = min(word_count / 15.0, 0.6)  # Max 0.6 at 15 words

            # Check for specific terms (positive indicators)
            specific_count = sum(1 for term in self.config.SPECIFIC_TERMS if term in prompt.lower())
            specificity_bonus = min(specific_count * 0.15, 0.4)  # Max 0.4 bonus

            # Penalty for vague words
            vague_count = sum(1 for vague in self.vague_prompt_indicators if vague in words)
            vague_penalty = min(vague_count * 0.1, 0.3)  # Max 0.3 penalty

            # Additional bonus for detailed descriptions
            detail_indicators = [
                'button', 'link', 'field', 'form', 'menu', 'dropdown',
                'checkbox', 'radio', 'input', 'textarea', 'select',
                'modal', 'dialog', 'popup', 'tab', 'panel', 'section',
                'header', 'footer', 'sidebar', 'navigation', 'breadcrumb'
            ]
            detail_count = sum(1 for indicator in detail_indicators if indicator in prompt.lower())
            detail_bonus = min(detail_count * 0.1, 0.2)  # Max 0.2 bonus

            score = length_score + specificity_bonus + detail_bonus - vague_penalty
            return max(0.1, min(1.0, score))  # Minimum 0.1, maximum 1.0

        except Exception as e:
            logger.error(f"Error calculating specificity score: {e}")
            return 0.3
    
    def _calculate_semantic_score(self, prompt: str, search_results: List[Dict]) -> float:
        """Calculate semantic alignment with knowledge base."""
        try:
            if not search_results:
                return 0.3
            
            # Average of top 3 similarity scores
            top_scores = [result['score'] for result in search_results[:3]]
            if not top_scores:
                return 0.3
            
            return sum(top_scores) / len(top_scores)
            
        except Exception as e:
            logger.error(f"Error calculating semantic score: {e}")
            return 0.3
    
    def _has_sufficient_context(self, prompt: str, search_results: List[Dict]) -> bool:
        """Check if prompt has sufficient context from knowledge base."""
        try:
            # Check if we have high-quality search results
            high_quality_results = [r for r in search_results if r['score'] > self.min_similarity_threshold]
            very_relevant_results = [r for r in search_results if r['score'] > 0.75]

            # Check prompt length and specificity
            word_count = len(prompt.split())

            # Check for specific UI elements mentioned
            has_specific_elements = any(term in prompt.lower() for term in self.config.SPECIFIC_TERMS)

            # Check for action words
            action_words = ['click', 'type', 'enter', 'select', 'navigate', 'submit', 'verify']
            has_actions = any(action in prompt.lower() for action in action_words)

            # More lenient context requirements
            context_sufficient = (
                len(very_relevant_results) >= 1 or  # At least one very relevant result
                len(high_quality_results) >= 2      # Or two good results
            )

            prompt_detailed = (
                word_count >= self.config.MIN_PROMPT_LENGTH and
                (has_specific_elements or has_actions)
            )

            return context_sufficient and prompt_detailed

        except Exception as e:
            logger.error(f"Error checking context sufficiency: {e}")
            return False
    
    def _find_vague_indicators(self, prompt: str) -> List[str]:
        """Find vague indicators in the prompt."""
        words = prompt.lower().split()
        return [indicator for indicator in self.vague_prompt_indicators if indicator in words]
    
    def _find_specific_elements(self, prompt: str) -> List[str]:
        """Find specific UI elements mentioned in the prompt."""
        specific_elements = [
            'button', 'link', 'field', 'form', 'menu', 'dropdown',
            'checkbox', 'radio', 'input', 'textarea', 'select',
            'modal', 'dialog', 'popup', 'tab', 'panel'
        ]
        words = prompt.lower().split()
        return [element for element in specific_elements if element in words]
    
    def _assess_action_clarity(self, prompt: str) -> Dict[str, Any]:
        """Assess how clear the actions are in the prompt."""
        action_words = [
            'click', 'type', 'enter', 'select', 'choose', 'navigate',
            'scroll', 'drag', 'drop', 'hover', 'submit', 'save'
        ]
        words = prompt.lower().split()
        found_actions = [action for action in action_words if action in words]
        
        return {
            'actions_found': found_actions,
            'action_count': len(found_actions),
            'has_clear_actions': len(found_actions) > 0
        }

    def generate_questions(self, user_prompt: str, context_results: List[Dict[str, Any]]) -> List[str]:
        """Generate clarifying questions based on prompt analysis."""
        try:
            # Prepare context from search results - use more relevant results
            context_texts = []
            relevant_results = [r for r in context_results if r.get('score', 0) > 0.6]

            for result in relevant_results[:self.config.CONTEXT_CHUNKS_FOR_QUESTIONS]:
                context_texts.append(f"- {result['text'][:200]}...")

            context_text = "\n".join(context_texts) if context_texts else "No relevant context found in knowledge base."

            user_message = f"""Original prompt: "{user_prompt}"

Available context from knowledge base:
{context_text}

Analyze the prompt and generate 2-3 specific clarifying questions that will eliminate ambiguity and prevent hallucination in browser automation.

ANALYSIS CHECKLIST:
- Are specific element identifiers mentioned (IDs, classes, exact text)?
- Are exact actions clearly defined with target elements?
- Are verification steps specific and measurable?
- Is required data/input explicitly provided?
- Are page URLs or navigation paths specified?

QUESTION PRIORITY:
1. Missing element identifiers (exact button text, field labels, IDs)
2. Undefined verification criteria (specific success messages, URL changes)
3. Missing input data (exact values to enter)
4. Unclear navigation (specific pages, URLs, paths)

Generate questions that would help provide these missing specific details. Each question should ask for exact, actionable information."""

            # Use the model from configuration
            model_name = self.config.QUESTION_GENERATION_MODEL
            
            logger.info(f"Using model: {model_name} for question generation")
            logger.debug(f"System prompt length: {len(self.config.QUESTION_GENERATION_SYSTEM_PROMPT)}")
            logger.debug(f"User message length: {len(user_message)}")

            try:
                response = self.openai_client.chat.completions.create(
                    model=model_name,
                    messages=[
                        {"role": "system", "content": self.config.QUESTION_GENERATION_SYSTEM_PROMPT},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=self.config.QUESTION_GENERATION_TEMPERATURE
                )
            except Exception as api_error:
                logger.error(f"API call failed for question generation: {api_error}")
                return self.config.get_fallback_questions()

            # Handle potential null response from Gemini API
            response_content = response.choices[0].message.content
            if response_content is None:
                logger.warning(f"Received null response from Gemini API for question generation. Model: {model_name}")
                logger.warning(f"Response object: {response}")
                return self.config.get_fallback_questions()
            
            questions_text = response_content.strip()

            # Try to parse as JSON array
            import json
            try:
                questions = json.loads(questions_text)
                if isinstance(questions, list):
                    # Validate and improve questions
                    validated_questions = self._validate_and_improve_questions(questions, user_prompt)
                    return validated_questions[:self.config.MAX_QUESTIONS_PER_ITERATION]
            except json.JSONDecodeError:
                pass

            # Fallback: split by lines and clean up
            questions = [q.strip('- ').strip() for q in questions_text.split('\n') if q.strip()]
            questions = [q for q in questions if q and not q.startswith('[') and len(q) > 10]
            
            # Validate and improve the questions
            validated_questions = self._validate_and_improve_questions(questions, user_prompt)
            return validated_questions[:self.config.MAX_QUESTIONS_PER_ITERATION] if validated_questions else self.config.get_fallback_questions()

        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return self.config.get_fallback_questions()

    def _validate_and_improve_questions(self, questions: List[str], user_prompt: str) -> List[str]:
        """Validate and improve generated questions to be more actionable for AI agents."""
        try:
            improved_questions = []
            user_prompt_lower = user_prompt.lower()
            
            for question in questions:
                if not question or len(question.strip()) < 10:
                    continue
                    
                question = question.strip()
                question_lower = question.lower()
                
                # Improve overly technical questions to be more practical
                if "exact id" in question_lower or "class name" in question_lower:
                    if "test" in user_prompt_lower:
                        question = "What specific actions should be performed to test this feature effectively?"
                    else:
                        question = "What are the main interactive elements that should be used for this task?"
                        
                elif "css selector" in question_lower or "xpath" in question_lower:
                    question = "What type of user interactions should be performed (clicking, typing, selecting, etc.)?"
                    
                elif "exact text" in question_lower and "button" in question_lower:
                    question = "What should happen when the main action is performed?"
                    
                elif "specific message" in question_lower and "verify" in question_lower:
                    question = "How can you tell if the feature is working correctly after using it?"
                
                # Focus on actionable testing scenarios
                if any(keyword in question_lower for keyword in ["action", "interact", "test", "verify", "behavior"]):
                    improved_questions.append(question)
                elif len(improved_questions) < 2:  # Accept if we need more questions
                    improved_questions.append(question)
            
            # Ensure we have practical questions
            if not improved_questions:
                return self.config.get_fallback_questions()
                
            return improved_questions
            
        except Exception as e:
            logger.error(f"Error validating questions: {e}")
            return self.config.get_fallback_questions()

    def refine_prompt(self, base_prompt: str, questions: List[str], answers: List[str]) -> str:
        """Refine the prompt using questions and answers."""
        try:
            # Combine questions and answers
            qa_pairs = []
            for q, a in zip(questions, answers):
                if a.strip():  # Only include non-empty answers
                    qa_pairs.append(f"Q: {q}\nA: {a}")

            qa_text = "\n\n".join(qa_pairs) if qa_pairs else "No additional clarification provided."

            user_message = f"""Original prompt: {base_prompt}

Clarifying information:
{qa_text}

Create a refined, step-by-step prompt that incorporates this additional information for browser automation.

CRITICAL: You MUST follow this exact format structure:

OBJECTIVE: [Clear, concise goal based on the original prompt]

STEPS:
1. [First actionable step - be specific if details are provided in clarifying information]
2. [Second actionable step - focus on logical progression]
3. [Continue with logical sequence]
4. [Include verification steps where appropriate]
5. [Final verification of the objective]

FORMATTING RULES:
- Start with "OBJECTIVE:" followed by a clear goal statement
- Follow with "STEPS:" and then numbered steps (1., 2., 3., etc.)
- Each step should be actionable and specific
- Use information from the clarifying answers when available
- Do NOT add "Additional details", "NOTE:", or Q&A sections
- Do NOT mention missing information - focus on what CAN be done

The output should be clean, structured instructions that an AI agent can immediately execute."""

            # Use the model from configuration
            model_name = self.config.PROMPT_REFINEMENT_MODEL
            
            logger.info(f"Using model: {model_name} for prompt refinement")
            logger.debug(f"System prompt length: {len(self.config.PROMPT_REFINEMENT_SYSTEM_PROMPT)}")
            logger.debug(f"User message length: {len(user_message)}")
            
            try:
                response = self.openai_client.chat.completions.create(
                    model=model_name,
                    messages=[
                        {"role": "system", "content": self.config.PROMPT_REFINEMENT_SYSTEM_PROMPT},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=self.config.PROMPT_REFINEMENT_TEMPERATURE
                )
            except Exception as api_error:
                logger.error(f"API call failed for prompt refinement: {api_error}")
                return base_prompt

            # Handle potential null response from Gemini API
            response_content = response.choices[0].message.content
            if response_content is None:
                logger.warning(f"Received null response from Gemini API for prompt refinement. Model: {model_name}")
                logger.warning(f"Response object: {response}")
                logger.warning(f"Choices length: {len(response.choices) if response.choices else 'No choices'}")
                if response.choices and len(response.choices) > 0:
                    logger.warning(f"First choice: {response.choices[0]}")
                return base_prompt  # Return original prompt if refinement fails
            
            refined_prompt = response_content.strip()
            
            # Validate the refined prompt for potential hallucination
            refined_prompt = self._validate_and_sanitize_prompt(refined_prompt, base_prompt, answers)
            
            return refined_prompt

        except Exception as e:
            logger.error(f"Error refining prompt: {e}")
            # Fallback: create a structured prompt following the required format
            combined = f"OBJECTIVE: {base_prompt.strip()}\n\nSTEPS:\n"
            step_num = 1
            
            # Add steps based on the available answers
            for q, a in zip(questions, answers):
                if a.strip() and not any(generic in a.lower() for generic in [
                    "find the main", "interact with", "use standard test data", 
                    "explore and interact", "check for visual feedback"
                ]):
                    combined += f"{step_num}. {a.strip()}\n"
                    step_num += 1
            
            # If no specific answers, add generic steps
            if step_num == 1:
                combined += f"{step_num}. Navigate to the specified page or section\n"
                combined += f"{step_num + 1}. Locate and interact with the relevant elements\n"
                combined += f"{step_num + 2}. Verify the expected functionality works correctly\n"
            
            return combined

    def _validate_and_sanitize_prompt(self, refined_prompt: str, base_prompt: str, answers: List[str]) -> str:
        """Validate the refined prompt for proper formatting and structure."""
        try:
            prompt_lower = refined_prompt.lower()
            
            # Check if the prompt follows the expected format (OBJECTIVE: and STEPS:)
            has_objective = 'objective:' in prompt_lower
            has_steps = 'steps:' in prompt_lower
            
            # If the refined prompt doesn't follow the expected format, restructure it
            if not (has_objective and has_steps):
                logger.info("Restructuring prompt to follow the required format")
                
                # Extract meaningful content from the refined prompt or use base prompt
                if refined_prompt.strip() and len(refined_prompt.strip()) > len(base_prompt.strip()):
                    # Use the refined content but restructure it
                    content_lines = [line.strip() for line in refined_prompt.split('\n') if line.strip()]
                    
                    # Remove common prefixes and format as steps
                    clean_lines = []
                    for line in content_lines:
                        # Skip headers and empty lines
                        if any(header in line.lower() for header in ['objective:', 'steps:', 'prerequisites:', 'expected outcome:']):
                            continue
                        # Clean up numbered steps
                        line = line.lstrip('0123456789.- ').strip()
                        if line and len(line) > 10:  # Only meaningful lines
                            clean_lines.append(line)
                    
                    # Build structured prompt
                    structured_prompt = f"OBJECTIVE: {base_prompt.strip()}\n\nSTEPS:\n"
                    
                    step_num = 1
                    for line in clean_lines[:5]:  # Max 5 steps
                        if not any(generic in line.lower() for generic in [
                            "note: specific", "details needed", "information is needed",
                            "additional details:", "q:", "a:"
                        ]):
                            structured_prompt += f"{step_num}. {line}\n"
                            step_num += 1
                    
                    # Ensure we have at least some steps
                    if step_num == 1:
                        structured_prompt += f"1. Navigate to the target page or section\n"
                        structured_prompt += f"2. Locate and interact with the main elements for: {base_prompt.strip()}\n"
                        structured_prompt += f"3. Verify the expected functionality works correctly\n"
                    
                    return structured_prompt
                else:
                    # Fall back to base prompt with structured format
                    return f"OBJECTIVE: {base_prompt.strip()}\n\nSTEPS:\n1. Navigate to the target page or section\n2. Locate and interact with the main elements related to the objective\n3. Verify the expected functionality works correctly\n"
            
            # If it already has the right structure, just clean it up slightly
            return refined_prompt.strip()
            
        except Exception as e:
            logger.error(f"Error validating prompt: {e}")
            return f"OBJECTIVE: {base_prompt.strip()}\n\nSTEPS:\n1. Navigate to the target page or section\n2. Locate and interact with the main elements related to the objective\n3. Verify the expected functionality works correctly\n"
