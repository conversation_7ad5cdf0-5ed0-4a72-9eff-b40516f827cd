"""
DrCode: UI Testing Backend API Server
Provides auto-refinement and vector database management functionality
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager
import os
import httpx
from fastapi import Request
import json

from controllers.auto_refinement_controller import router as auto_refinement_router
from controllers.vector_db_controller import router as vector_db_router
from controllers.health_controller import router as health_router
from controllers.document_evaluation_controller import router as document_evaluation_router
from controllers.testcase_controller import router as testcase_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 DrCode: UI Testing Backend API Server starting up...")
    yield
    # Shutdown
    logger.info("🛑 DrCode: UI Testing Backend API Server shutting down...")


# Create FastAPI app with lifespan
app = FastAPI(
    title="DrCode: UI Testing Backend API",
    description="Backend API for DrCode: UI Testing auto-refinement and vector database management",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router, prefix="/api/v1")
app.include_router(auto_refinement_router, prefix="/api/v1")
app.include_router(vector_db_router, prefix="/api/v1")
app.include_router(document_evaluation_router, prefix="/api/v1")
app.include_router(testcase_router, prefix="/api/v1")


@app.get("/")
async def root():
    return {
        "message": "Welcome to DrCode: UI Testing Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception handler: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"}
    )

OPENAI_API_URL = os.getenv("OPENAI_API_URL", "https://generativelanguage.googleapis.com/v1beta/openai")
DEFAULT_PLAN_MODEL = os.getenv("DEFAULT_PLAN_MODEL", "gemini-2.5-flash")
DEFAULT_NAVIGATION_MODEL = os.getenv("DEFAULT_NAVIGATION_MODEL", "gemini-2.5-flash")
DEFAULT_VALIDATION_MODEL = os.getenv("DEFAULT_VALIDATION_MODEL", "gemini-2.5-flash")
# GEMINI_PROXY_TARGET = os.getenv("GEMINI_PROXY_TARGET", "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions")

@app.post("/chat/completions")
async def proxy_chat_completions(request: Request):
    try:
        payload = await request.json()
        if payload["model"] == "planner":
            payload["model"] = DEFAULT_PLAN_MODEL
        elif payload["model"] == "navigator":
            payload["model"] = DEFAULT_NAVIGATION_MODEL
        elif payload["model"] == "validator":
            payload["model"] = DEFAULT_VALIDATION_MODEL
        else:
            return JSONResponse(status_code=400, content={"detail": "Invalid model"})
            # payload["model"] = DEFAULT_PLAN_MODEL
            
        # Build new headers for the outgoing request
        out_headers = {}
        for k, v in request.headers.items():
            lk = k.lower()
            if lk in ('host', 'content-length'):
                continue
            out_headers[k] = v
        # Override Content-Type and Authorization with Gemini API key
        out_headers["Content-Type"] = "application/json"
        out_headers["Authorization"] = f"Bearer {os.getenv('OPENAI_API_KEY', '')}"  # Contains Gemini API key

        async with httpx.AsyncClient(base_url=OPENAI_API_URL, timeout=60) as client:
            resp = await client.post("/chat/completions", json=payload, headers=out_headers)
        return JSONResponse(status_code=resp.status_code, content=resp.json())
    except httpx.HTTPStatusError as e:
        logger.error(f"Proxy error: {e.response.status_code} {e.response.text}")
        return JSONResponse(status_code=e.response.status_code, content={"detail": e.response.text})
    except Exception as e:
        logger.error(f"Proxy exception: {str(e)}")
        return JSONResponse(status_code=500, content={"detail": f"Proxy error: {str(e)}"})


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,  # Different port from web-ui
        reload=True,
        log_level="info"
    )
