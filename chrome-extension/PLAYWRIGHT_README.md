# Playwright Script Generation

This Chrome extension now includes automatic Playwright script generation from tracked browser actions. The system tracks all actions performed by the AI agent and converts them into executable Playwright test scripts.

## Features

### Action Tracking
- **Automatic Tracking**: All browser actions (clicks, inputs, navigation, etc.) are automatically tracked
- **Session Management**: Actions are grouped by session with unique session IDs
- **Selector Extraction**: Captures CSS selectors and XPath information for elements
- **Parameter Recording**: Stores all action parameters for accurate script generation

### Script Generation
- **Multiple Languages**: Support for JavaScript, TypeScript, Python, Java, and C#
- **Customizable Options**: 
  - Include/exclude comments
  - Include/exclude assertions
  - Include/exclude wait statements between actions
  - **Automatic Page Load Waits**: Always includes `await page.waitForLoadState('networkidle')` after navigation actions
- **Real-time Preview**: View generated scripts before export
- **Export Functionality**: Download scripts as files
- **Re-run Actions**: Execute tracked actions directly in the browser without AI agents (only browser interactions, AI completion messages are skipped)

### Supported Actions
- `go_to_url` - Navigate to URLs
- `click_element` - Click on elements
- `input_text` - Fill form fields
- `search_google` - Google search actions
- `scroll_to_text` - Scroll to specific text
- `send_keys` - Keyboard input
- `select_dropdown_option` - Dropdown selection
- `wait` - Explicit waits
- `scroll_to_percent` - Scroll to percentage
- `scroll_to_top` - Scroll to top
- `scroll_to_bottom` - Scroll to bottom

## Usage

### In the Side Panel
1. Click the document icon in the header to open the Playwright Generator
2. View session information and tracked action count
3. Configure script options (language, comments, assertions, waits)
4. Generate script preview
5. Export script to file
6. **Re-run Actions**: Click "Re-run Actions" to execute the same steps without AI agents

### API Integration
The system can be accessed programmatically through the background service:

```typescript
// Generate a script
const script = PlaywrightGenerator.generateScript({
  language: 'javascript',
  includeComments: true,
  includeAssertions: true,
  includeWaitFor: true
});

// Export to file
PlaywrightGenerator.exportToFile({
  language: 'typescript',
  filename: 'my-test.ts'
});

// Get session info
const info = PlaywrightGenerator.getSessionInfo();
```

## Message Types

### Background Service Messages
- `generate_playwright_script` - Generate script with options
- `export_playwright_script` - Export script to file
- `get_action_tracker_info` - Get session and language info
- `reset_action_tracker` - Reset action tracking
- `export_actions_json` - Export actions as JSON
- `import_actions_json` - Import actions from JSON

## Generated Script Structure

### JavaScript/TypeScript Example
```javascript
const { test, expect } = require('@playwright/test');

test('Automated test from tracked actions', async ({ page }) => {
  // go_to_url: {"url":"https://example.com"}
  await page.goto('https://example.com');
  await page.waitForLoadState('networkidle');
  
  // click_element: {"index":0}
  await page.locator('[data-testid="element-0"]').click();
  await page.waitForLoadState('networkidle');
  
  // input_text: {"index":1,"text":"Hello World"}
  await page.locator('[data-testid="element-1"]').fill('Hello World');
  
  await expect(page.locator('[data-testid="element-1"]')).toHaveValue('Hello World');
});
```

### Python Example
```python
from playwright.sync_api import sync_playwright

def test_automated_actions():
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # go_to_url: {"url":"https://example.com"}
        page.goto('https://example.com')
        
        # click_element: {"index":0}
        page.locator('[data-testid="element-0"]').click()
        
        # input_text: {"index":1,"text":"Hello World"}
        page.locator('[data-testid="element-1"]').fill('Hello World')
        
        expect(page.locator('[data-testid="element-1"]')).to_have_value('Hello World')
        
        browser.close()
```

## Architecture

### Components
1. **ActionTracker** (`src/background/services/actionTracker.ts`)
   - Tracks all actions with metadata
   - Converts actions to Playwright code
   - Manages session state

2. **PlaywrightGenerator** (`src/background/services/playwrightGenerator.ts`)
   - High-level API for script generation
   - File export functionality
   - Session management

3. **PlaywrightGenerator Component** (`pages/side-panel/src/components/PlaywrightGenerator.tsx`)
   - React UI for script generation
   - Real-time preview
   - Export controls

### Integration Points
- **Navigator Agent**: Tracks actions during execution
- **Background Service**: Handles message communication
- **Side Panel**: Provides user interface

## Configuration

### Action Tracking Options
- Session-based tracking with unique IDs
- Automatic selector extraction
- XPath fallback support
- Parameter validation

### Script Generation Options
- Language selection (JS, TS, Python, Java, C#)
- Comment inclusion
- Assertion generation
- Wait statement insertion

## Future Enhancements

- **Custom Selectors**: Allow users to define custom selectors
- **Script Templates**: Pre-defined script templates
- **Test Framework Integration**: Direct integration with test runners
- **Action Editing**: Manual editing of tracked actions
- **Script Validation**: Validate generated scripts
- **Batch Export**: Export multiple scripts at once

## Troubleshooting

### Common Issues
1. **No Actions Tracked**: Ensure the extension is active and actions are being performed
2. **Script Generation Fails**: Check browser console for error messages
3. **Export Issues**: Verify file permissions and browser download settings

### Debug Information
- Session IDs are logged for tracking
- Action parameters are stored with timestamps
- Error messages include detailed context

## Contributing

When adding new actions:
1. Update `actionTracker.ts` with new action conversion logic
2. Add corresponding Playwright code generation
3. Update this documentation
4. Test with various languages and options 